package com.hvisions.rawmaterial.utils;

import com.hvisions.rawmaterial.dto.visualization.WarehouseVisualizationDTO;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 原辅料仓库可视化模拟数据生成器
 * 
 * <AUTHOR>
 * @date 2025-09-15
 */
@Component
public class WarehouseVisualizationDataGenerator {

    /**
     * 生成高粱可视化数据
     */
    public WarehouseVisualizationDTO generateSorghumVisualizationData() {
        WarehouseVisualizationDTO data = new WarehouseVisualizationDTO();
        
        // 原辅料仓库信息
        WarehouseVisualizationDTO.RawMaterialWarehouseDTO rawMaterialWarehouse = new WarehouseVisualizationDTO.RawMaterialWarehouseDTO();
        rawMaterialWarehouse.setTotalStock(new BigDecimal("40000"));
        
        List<WarehouseVisualizationDTO.MaterialGradeDTO> rawMaterialGrades = new ArrayList<>();
        rawMaterialGrades.add(createMaterialGrade("LGL1", "高粱一级", "10000", "8000", "12000"));
        rawMaterialGrades.add(createMaterialGrade("LGL2", "高粱二级", "10000", "8000", "12000"));
        rawMaterialGrades.add(createMaterialGrade("LGL3", "高粱三级", "10000", "8000", "12000"));
        rawMaterialGrades.add(createMaterialGrade("LGL4", "高粱四级", "10000", "8000", "12000"));
        rawMaterialWarehouse.setMaterialGrades(rawMaterialGrades);
        
        // 前处理储存仓信息
        WarehouseVisualizationDTO.PreProcessingStorageDTO preProcessingStorage = new WarehouseVisualizationDTO.PreProcessingStorageDTO();
        preProcessingStorage.setTotalStorage(new BigDecimal("11347"));
        preProcessingStorage.setMaxCapacity(new BigDecimal("3000000"));
        
        List<WarehouseVisualizationDTO.MaterialGradeDTO> preProcessingGrades = new ArrayList<>();
        preProcessingGrades.add(createMaterialGrade("LGL1", "高粱一级", "2300", "2000", "3000"));
        preProcessingGrades.add(createMaterialGrade("LGL2", "高粱二级", "3300", "2000", "3000"));
        preProcessingGrades.add(createMaterialGrade("LGL3", "高粱三级", "3000", "2000", "3000"));
        preProcessingGrades.add(createMaterialGrade("LGL4", "高粱四级", "2747", "2000", "3000"));
        preProcessingStorage.setMaterialGrades(preProcessingGrades);
        
        // 后处理暂存仓信息
        WarehouseVisualizationDTO.PostProcessingStorageDTO postProcessingStorage = new WarehouseVisualizationDTO.PostProcessingStorageDTO();
        postProcessingStorage.setTotalStorage(new BigDecimal("10000"));
        
        List<WarehouseVisualizationDTO.SiloInfoDTO> silos = new ArrayList<>();
        silos.add(createSilo("01", "01#筒仓", "LGL1", "11347", "10000", "30000", "GL2025094184", "黑龙江"));
        silos.add(createSilo("02", "02#筒仓", "LGL1", "11347", "10000", "30000", "GL2025094184", "黑龙江"));
        silos.add(createSilo("03", "03#筒仓", "LGL1", "11347", "10000", "30000", "GL2025094184", "黑龙江"));
        silos.add(createSilo("04", "04#筒仓", "LGL1", "11347", "10000", "30000", "GL2025094184", "黑龙江"));
        postProcessingStorage.setSilos(silos);
        
        List<WarehouseVisualizationDTO.MaterialGradeDTO> postProcessingGrades = new ArrayList<>();
        postProcessingGrades.add(createMaterialGrade("LGL1", "高粱一级", "2300", "2000", "3000"));
        postProcessingGrades.add(createMaterialGrade("LGL2", "高粱二级", "3300", "2000", "3000"));
        postProcessingGrades.add(createMaterialGrade("LGL3", "高粱三级", "3000", "2000", "3000"));
        postProcessingGrades.add(createMaterialGrade("LGL4", "高粱四级", "2747", "2000", "3000"));
        postProcessingGrades.add(createMaterialGrade("T11", "T11", "3000", "2000", "3000"));
        postProcessingGrades.add(createMaterialGrade("T12", "T12", "2747", "2000", "3000"));
        postProcessingGrades.add(createMaterialGrade("T13", "T13", "1300", "1000", "2000"));
        postProcessingGrades.add(createMaterialGrade("T14", "T14", "4210", "3000", "5000"));
        postProcessingGrades.add(createMaterialGrade("T15", "T15", "2391", "2000", "3000"));
        postProcessingGrades.add(createMaterialGrade("T16", "T16", "2141", "2000", "3000"));
        postProcessingGrades.add(createMaterialGrade("T17", "T17", "5172", "4000", "6000"));
        postProcessingGrades.add(createMaterialGrade("T18", "T18", "1728", "1500", "2000"));
        postProcessingGrades.add(createMaterialGrade("T19", "T19", "1261", "1000", "2000"));
        postProcessingStorage.setMaterialGrades(postProcessingGrades);
        
        // 车间信息
        List<WarehouseVisualizationDTO.WorkshopDTO> workshops = new ArrayList<>();
        workshops.add(createWorkshop("710-1", "710一车间", "LGL1", "11347", "GL2025094184", "黑龙江"));
        workshops.add(createWorkshop("710-2", "710二车间", "LGL1", "11347", "GL2025094184", "黑龙江"));
        workshops.add(createWorkshop("710-3", "710三车间", "LGL1", "11347", "GL2025094184", "黑龙江"));
        workshops.add(createWorkshop("710-4", "710四车间", "LGL1", "11347", "GL2025094184", "黑龙江"));
        
        data.setRawMaterialWarehouse(rawMaterialWarehouse);
        data.setPreProcessingStorage(preProcessingStorage);
        data.setPostProcessingStorage(postProcessingStorage);
        data.setWorkshops(workshops);
        
        return data;
    }

    /**
     * 生成稻壳可视化数据
     */
    public WarehouseVisualizationDTO generateRiceHuskVisualizationData() {
        WarehouseVisualizationDTO data = new WarehouseVisualizationDTO();

        // 原辅料仓库信息
        WarehouseVisualizationDTO.RawMaterialWarehouseDTO rawMaterialWarehouse = new WarehouseVisualizationDTO.RawMaterialWarehouseDTO();
        rawMaterialWarehouse.setTotalStock(new BigDecimal("40000"));

        List<WarehouseVisualizationDTO.MaterialGradeDTO> rawMaterialGrades = new ArrayList<>();
        rawMaterialGrades.add(createMaterialGrade("DK", "稻壳", "40000", "35000", "50000"));
        rawMaterialWarehouse.setMaterialGrades(rawMaterialGrades);
        
        // 前处理储存仓信息
        WarehouseVisualizationDTO.PreProcessingStorageDTO preProcessingStorage = new WarehouseVisualizationDTO.PreProcessingStorageDTO();
        preProcessingStorage.setTotalStorage(new BigDecimal("11347"));
        preProcessingStorage.setMaxCapacity(new BigDecimal("3000000"));

        List<WarehouseVisualizationDTO.MaterialGradeDTO> preProcessingGrades = new ArrayList<>();
        preProcessingGrades.add(createMaterialGrade("DK", "稻壳", "11347", "10000", "30000"));
        preProcessingStorage.setMaterialGrades(preProcessingGrades);
        
        // 后处理暂存仓信息
        WarehouseVisualizationDTO.PostProcessingStorageDTO postProcessingStorage = new WarehouseVisualizationDTO.PostProcessingStorageDTO();
        postProcessingStorage.setTotalStorage(new BigDecimal("10000"));

        List<WarehouseVisualizationDTO.SiloInfoDTO> silos = new ArrayList<>();
        silos.add(createSilo("01", "01#筒仓", "DK", "11347", "10000", "30000", "DK2025094184", "江苏"));
        silos.add(createSilo("02", "02#筒仓", "DK", "11347", "10000", "30000", "DK2025094184", "江苏"));
        silos.add(createSilo("03", "03#筒仓", "DK", "11347", "10000", "30000", "DK2025094184", "江苏"));
        silos.add(createSilo("04", "04#筒仓", "DK", "11347", "10000", "30000", "DK2025094184", "江苏"));
        postProcessingStorage.setSilos(silos);

        List<WarehouseVisualizationDTO.MaterialGradeDTO> postProcessingGrades = new ArrayList<>();
        postProcessingGrades.add(createMaterialGrade("DK", "稻壳", "10000", "10000", "30000"));
        postProcessingStorage.setMaterialGrades(postProcessingGrades);
        
        // 车间信息
        List<WarehouseVisualizationDTO.WorkshopDTO> workshops = new ArrayList<>();
        // 709车间系列
        workshops.add(createWorkshop("709-1", "709一车间", "DK", "11347", "DK2025094184", "江苏"));
        workshops.add(createWorkshop("709-2", "709二车间", "DK", "11347", "DK2025094184", "江苏"));
        workshops.add(createWorkshop("709-3", "709三车间", "DK", "11347", "DK2025094184", "江苏"));
        workshops.add(createWorkshop("709-4", "709四车间", "DK", "11347", "DK2025094184", "江苏"));
        // 710车间系列
        workshops.add(createWorkshop("710-1", "710一车间", "DK", "11347", "DK2025094184", "江苏"));
        workshops.add(createWorkshop("710-2", "710二车间", "DK", "11347", "DK2025094184", "江苏"));
        workshops.add(createWorkshop("710-3", "710三车间", "DK", "11347", "DK2025094184", "江苏"));
        workshops.add(createWorkshop("710-4", "710四车间", "DK", "11347", "DK2025094184", "江苏"));
        
        // 熟稻壳缓存仓信息
        WarehouseVisualizationDTO.CookedRiceHuskStorageDTO cookedRiceHuskStorage = new WarehouseVisualizationDTO.CookedRiceHuskStorageDTO();
        List<WarehouseVisualizationDTO.SiloInfoDTO> cookedSilos = new ArrayList<>();
        cookedSilos.add(createSilo("1", "1#筒仓", "DK", "11347", "10000", "30000", "DK2025094184", "江苏"));
        cookedSilos.add(createSilo("2", "2#筒仓", "DK", "11347", "10000", "30000", "DK2025094184", "江苏"));
        cookedSilos.add(createSilo("3", "3#筒仓", "DK", "11347", "10000", "30000", "DK2025094184", "江苏"));
        cookedRiceHuskStorage.setSilos(cookedSilos);

        // 线边仓库信息
        WarehouseVisualizationDTO.LineSideWarehouseDTO lineSideWarehouse = new WarehouseVisualizationDTO.LineSideWarehouseDTO();
        lineSideWarehouse.setTotalStock(new BigDecimal("40000"));

        // 中心缓存仓信息
        WarehouseVisualizationDTO.CenterStorageDTO centerStorage = new WarehouseVisualizationDTO.CenterStorageDTO();
        centerStorage.setTotalStock(new BigDecimal("10000"));

        List<WarehouseVisualizationDTO.CenterStorageDetailDTO> storageDetails = new ArrayList<>();
        storageDetails.add(createCenterStorageDetail("709", "2300"));
        storageDetails.add(createCenterStorageDetail("710", "3300"));
        storageDetails.add(createCenterStorageDetail("711", "3000"));
        storageDetails.add(createCenterStorageDetail("712", "2747"));
        storageDetails.add(createCenterStorageDetail("713", "1300"));
        storageDetails.add(createCenterStorageDetail("714", "4210"));
        storageDetails.add(createCenterStorageDetail("715", "2391"));
        storageDetails.add(createCenterStorageDetail("716", "2141"));
        storageDetails.add(createCenterStorageDetail("717", "5172"));
        storageDetails.add(createCenterStorageDetail("718", "1728"));
        storageDetails.add(createCenterStorageDetail("719", "1261"));
        centerStorage.setStorageDetails(storageDetails);

        data.setRawMaterialWarehouse(rawMaterialWarehouse);
        data.setPreProcessingStorage(preProcessingStorage);
        data.setPostProcessingStorage(postProcessingStorage);
        data.setWorkshops(workshops);
        data.setCookedRiceHuskStorage(cookedRiceHuskStorage);
        data.setLineSideWarehouse(lineSideWarehouse);
        data.setCenterStorage(centerStorage);

        return data;
    }

    /**
     * 生成小麦可视化数据
     */
    public WarehouseVisualizationDTO generateWheatVisualizationData() {
        WarehouseVisualizationDTO data = new WarehouseVisualizationDTO();
        
        // 原辅料仓库信息
        WarehouseVisualizationDTO.RawMaterialWarehouseDTO rawMaterialWarehouse = new WarehouseVisualizationDTO.RawMaterialWarehouseDTO();
        rawMaterialWarehouse.setTotalStock(new BigDecimal("30000"));
        
        List<WarehouseVisualizationDTO.MaterialGradeDTO> rawMaterialGrades = new ArrayList<>();
        rawMaterialGrades.add(createMaterialGrade("XM1", "小麦一级", "7500", "6000", "9000"));
        rawMaterialGrades.add(createMaterialGrade("XM2", "小麦二级", "7500", "6000", "9000"));
        rawMaterialGrades.add(createMaterialGrade("XM3", "小麦三级", "7500", "6000", "9000"));
        rawMaterialGrades.add(createMaterialGrade("XM4", "小麦四级", "7500", "6000", "9000"));
        rawMaterialWarehouse.setMaterialGrades(rawMaterialGrades);
        
        // 前处理储存仓信息
        WarehouseVisualizationDTO.PreProcessingStorageDTO preProcessingStorage = new WarehouseVisualizationDTO.PreProcessingStorageDTO();
        preProcessingStorage.setTotalStorage(new BigDecimal("8200"));
        preProcessingStorage.setMaxCapacity(new BigDecimal("2000000"));
        
        List<WarehouseVisualizationDTO.MaterialGradeDTO> preProcessingGrades = new ArrayList<>();
        preProcessingGrades.add(createMaterialGrade("XM1", "小麦一级", "2000", "1500", "2200"));
        preProcessingGrades.add(createMaterialGrade("XM2", "小麦二级", "2100", "1500", "2200"));
        preProcessingGrades.add(createMaterialGrade("XM3", "小麦三级", "2100", "1500", "2200"));
        preProcessingGrades.add(createMaterialGrade("XM4", "小麦四级", "2000", "1500", "2200"));
        preProcessingStorage.setMaterialGrades(preProcessingGrades);
        
        // 后处理暂存仓信息
        WarehouseVisualizationDTO.PostProcessingStorageDTO postProcessingStorage = new WarehouseVisualizationDTO.PostProcessingStorageDTO();
        postProcessingStorage.setTotalStorage(new BigDecimal("7800"));
        
        List<WarehouseVisualizationDTO.SiloInfoDTO> silos = new ArrayList<>();
        silos.add(createSilo("01", "01#筒仓", "XM1", "8200", "7000", "22000", "XM2025094184", "河南"));
        silos.add(createSilo("02", "02#筒仓", "XM1", "8200", "7000", "22000", "XM2025094184", "河南"));
        silos.add(createSilo("03", "03#筒仓", "XM1", "8200", "7000", "22000", "XM2025094184", "河南"));
        silos.add(createSilo("04", "04#筒仓", "XM1", "8200", "7000", "22000", "XM2025094184", "河南"));
        postProcessingStorage.setSilos(silos);
        
        List<WarehouseVisualizationDTO.MaterialGradeDTO> postProcessingGrades = new ArrayList<>();
        postProcessingGrades.add(createMaterialGrade("XM1", "小麦一级", "2000", "1500", "2200"));
        postProcessingGrades.add(createMaterialGrade("XM2", "小麦二级", "2100", "1500", "2200"));
        postProcessingGrades.add(createMaterialGrade("XM3", "小麦三级", "2100", "1500", "2200"));
        postProcessingGrades.add(createMaterialGrade("XM4", "小麦四级", "2000", "1500", "2200"));
        postProcessingStorage.setMaterialGrades(postProcessingGrades);
        
        // 车间信息
        List<WarehouseVisualizationDTO.WorkshopDTO> workshops = new ArrayList<>();
        workshops.add(createWorkshop("710-1", "710一车间", "XM1", "8200", "XM2025094184", "河南"));
        workshops.add(createWorkshop("710-2", "710二车间", "XM1", "8200", "XM2025094184", "河南"));
        workshops.add(createWorkshop("710-3", "710三车间", "XM1", "8200", "XM2025094184", "河南"));
        workshops.add(createWorkshop("710-4", "710四车间", "XM1", "8200", "XM2025094184", "河南"));
        
        data.setRawMaterialWarehouse(rawMaterialWarehouse);
        data.setPreProcessingStorage(preProcessingStorage);
        data.setPostProcessingStorage(postProcessingStorage);
        data.setWorkshops(workshops);
        
        return data;
    }

    private WarehouseVisualizationDTO.MaterialGradeDTO createMaterialGrade(String code, String name, String current, String safety, String max) {
        WarehouseVisualizationDTO.MaterialGradeDTO grade = new WarehouseVisualizationDTO.MaterialGradeDTO();
        grade.setMaterialCode(code);
        grade.setMaterialName(name);
        grade.setCurrentStock(new BigDecimal(current));
        grade.setSafetyStock(new BigDecimal(safety));
        grade.setMaxCapacity(new BigDecimal(max));
        return grade;
    }

    private WarehouseVisualizationDTO.SiloInfoDTO createSilo(String number, String name, String materialCode, 
                                                            String current, String safety, String max, 
                                                            String batch, String origin) {
        WarehouseVisualizationDTO.SiloInfoDTO silo = new WarehouseVisualizationDTO.SiloInfoDTO();
        silo.setSiloNumber(number);
        silo.setSiloName(name);
        silo.setMaterialCode(materialCode);
        silo.setCurrentStock(new BigDecimal(current));
        silo.setSafetyStock(new BigDecimal(safety));
        silo.setMaxCapacity(new BigDecimal(max));
        silo.setBatchNumber(batch);
        silo.setOrigin(origin);
        return silo;
    }

    private WarehouseVisualizationDTO.WorkshopDTO createWorkshop(String number, String name, String materialCode, 
                                                               String current, String batch, String origin) {
        WarehouseVisualizationDTO.WorkshopDTO workshop = new WarehouseVisualizationDTO.WorkshopDTO();
        workshop.setWorkshopNumber(number);
        workshop.setWorkshopName(name);
        workshop.setMaterialCode(materialCode);
        workshop.setCurrentStock(new BigDecimal(current));
        workshop.setBatchNumber(batch);
        workshop.setOrigin(origin);
        return workshop;
    }

    private WarehouseVisualizationDTO.CenterStorageDetailDTO createCenterStorageDetail(String number, String stock) {
        WarehouseVisualizationDTO.CenterStorageDetailDTO detail = new WarehouseVisualizationDTO.CenterStorageDetailDTO();
        detail.setNumber(number);
        detail.setStock(new BigDecimal(stock));
        return detail;
    }
}
