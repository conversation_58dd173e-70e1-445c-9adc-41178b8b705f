package com.hvisions.rawmaterial.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.dto.visualization.SiloVisualizationDTO;
import com.hvisions.rawmaterial.dto.visualization.SiloVisualizationQueryDTO;
import com.hvisions.rawmaterial.entity.TMpdRlManagement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 原辅料可视化Mapper
 * @author: z19235
 */
@Mapper
public interface RawMaterialVisualizationMapper extends BaseMapper<TMpdRlManagement> {

    /**
     * 获取筒仓可视化数据
     * @param queryDTO 查询参数
     * @return 筒仓可视化数据列表
     */
    List<SiloVisualizationDTO> getSiloVisualization(@Param("queryDTO") SiloVisualizationQueryDTO queryDTO);
    
    /**
     * 获取筒仓层级结构
     * @param materialType 物料类型
     * @param rlIds 有权限的筒仓ID列表
     * @return 筒仓层级结构
     */
    List<SiloVisualizationDTO> getSiloHierarchy(@Param("materialType") Integer materialType, 
                                               @Param("rlIds") List<Integer> rlIds);
}