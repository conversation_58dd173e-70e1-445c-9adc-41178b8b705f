package com.hvisions.rawmaterial.controller.ricehusk;

import com.hvisions.rawmaterial.dto.production.bran.transfer.detail.BranTransferDetailListDTO;
import com.hvisions.rawmaterial.dto.production.bran.transfer.order.BranTransferOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.bran.transfer.order.BranTransferOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.service.BranTransferOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description:熟糠传输工单-二期改为发料任务
 * @date 2022/4/22 10:18
 */
@RestController
@RequestMapping(value = "/bran/transfer/order")
@Api(tags = "熟糠传输工单-二期改为发料任务")
public class BranTransferOrderController {

    @Resource
    private BranTransferOrderService branTransferOrderService;


    @ApiOperation(value = "分页查询熟糠传输工单")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<BranTransferOrderPageDTO> getBranTransferOrderPageList(@RequestBody BranTransferOrderPageQueryDTO queryDTO) {
        return branTransferOrderService.getBranTransferOrderPageList(queryDTO);
    }

    @ApiOperation(value = "批量新增熟糠传输工单详情")
    @RequestMapping(value = "/detail/insert/batch", method = RequestMethod.POST)
    public Integer insertOrderDetail(@RequestBody List<DetailInsertDTO> detailInsertDTOS) {
        return branTransferOrderService.insertOrderDetail(detailInsertDTOS);
    }

    @ApiOperation(value = "分页查询熟糠传输工单详细")
    @RequestMapping(value = "/getBranTransferOrderDetailPageList", method = RequestMethod.POST)
    public Page<BranTransferDetailListDTO> getBranTransferOrderDetailPageList(@RequestBody BranTransferOrderPageQueryDTO queryDTO) {
        return branTransferOrderService.getBranTransferOrderDetailPageList(queryDTO);
    }
}
