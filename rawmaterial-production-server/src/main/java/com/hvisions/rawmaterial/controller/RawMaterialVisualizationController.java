package com.hvisions.rawmaterial.controller;

import com.hvisions.auth.client.DepartmentClient;
import com.hvisions.auth.client.UserClient;
import com.hvisions.auth.dto.user.DepartmentDTO;
import com.hvisions.auth.dto.user.UserDTO;
import com.hvisions.common.annotation.UserId;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.rawmaterial.dto.visualization.SiloVisualizationDTO;
import com.hvisions.rawmaterial.dto.visualization.SiloVisualizationQueryDTO;
import com.hvisions.rawmaterial.dto.visualization.WarehouseVisualizationDTO;
import com.hvisions.rawmaterial.service.materialsilo.RlPermissionService;
import com.hvisions.rawmaterial.service.RawMaterialVisualizationService;
import com.hvisions.rawmaterial.utils.WarehouseVisualizationDataGenerator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 原辅料可视化
 * @author: z19235
 * @time: 2023/07/10
 */
@RestController
@RequestMapping(value = "/raw-material/visualization")
@Api(tags = "原辅料可视化")
public class RawMaterialVisualizationController {

    @Resource
    private RawMaterialVisualizationService rawMaterialVisualizationService;
    
    @Resource
    private RlPermissionService rlPermissionService;
    
    @Resource
    private UserClient userClient;
    
    @Resource
    private DepartmentClient departmentClient;

    @Resource
    private WarehouseVisualizationDataGenerator dataGenerator;

    @ApiOperation(value = "获取高粱筒仓可视化数据")
    @RequestMapping(value = "/sorghum/get", method = RequestMethod.POST)
    public ResultVO<List<SiloVisualizationDTO>> getSorghumVisualization(
            @RequestBody SiloVisualizationQueryDTO queryDTO,
            @UserId @ApiParam(hidden = true) Integer userId) {
        // 设置物料类型为高粱(0)
        queryDTO.setMaterialType(0);
        // 获取当前用户信息
        ResultVO userResult = userClient.getUser(userId);
        if (userResult != null && userResult.getData() != null) {
            UserDTO userDTO = (UserDTO) userResult.getData();
            // 获取用户部门ID
            Integer deptId = userDTO.getDepartmentId();
            if (deptId != null) {
                // 获取部门及其子部门有权限的筒仓ID列表
                List<Integer> rlIds = getRlIdsByDeptIdWithChildren(deptId);
                queryDTO.setRlIds(rlIds);
            }
        }
        List<SiloVisualizationDTO> data = rawMaterialVisualizationService.getSiloVisualization(queryDTO);
        return ResultVO.success(data);
    }

    @ApiOperation(value = "获取稻壳筒仓可视化数据")
    @RequestMapping(value = "/rice-husk/get", method = RequestMethod.POST)
    public ResultVO<List<SiloVisualizationDTO>> getRiceHuskVisualization(
            @RequestBody SiloVisualizationQueryDTO queryDTO,
            @UserId @ApiParam(hidden = true) Integer userId
    ) {
        // 设置物料类型为稻壳(1)
        queryDTO.setMaterialType(1);
        // 获取当前用户信息
        ResultVO userResult = userClient.getUser(userId);
        if (userResult != null && userResult.getData() != null) {
            UserDTO userDTO = (UserDTO) userResult.getData();
            // 获取用户部门ID
            Integer deptId = userDTO.getDepartmentId();
            if (deptId != null) {
                // 获取部门及其子部门有权限的筒仓ID列表
                List<Integer> rlIds = getRlIdsByDeptIdWithChildren(deptId);
                queryDTO.setRlIds(rlIds);
            }
        }
        List<SiloVisualizationDTO> data = rawMaterialVisualizationService.getSiloVisualization(queryDTO);
        return ResultVO.success(data);
    }

    @ApiOperation(value = "获取小麦筒仓可视化数据")
    @RequestMapping(value = "/wheat/get", method = RequestMethod.POST)
    public ResultVO<List<SiloVisualizationDTO>> getWheatVisualization(
            @RequestBody SiloVisualizationQueryDTO queryDTO,
            @UserId @ApiParam(hidden = true) Integer userId
    ) {
        // 设置物料类型为小麦(2)
        queryDTO.setMaterialType(2);
        // 获取当前用户信息
        ResultVO userResult = userClient.getUser(userId);
        if (userResult != null && userResult.getData() != null) {
            UserDTO userDTO = (UserDTO) userResult.getData();
            // 获取用户部门ID
            Integer deptId = userDTO.getDepartmentId();
            if (deptId != null) {
                // 获取部门及其子部门有权限的筒仓ID列表
                List<Integer> rlIds = getRlIdsByDeptIdWithChildren(deptId);
                queryDTO.setRlIds(rlIds);
            }
        }
        List<SiloVisualizationDTO> data = rawMaterialVisualizationService.getSiloVisualization(queryDTO);
        return ResultVO.success(data);
    }
    
    /**
     * 获取部门及其所有子部门有权限的筒仓ID列表
     * @param deptId 部门ID
     * @return 筒仓ID列表
     */
    private List<Integer> getRlIdsByDeptIdWithChildren(Integer deptId) {
        // 获取部门及其所有子部门ID
        List<Integer> allDeptIds = new ArrayList<>();
        allDeptIds.add(deptId);
        // 递归获取子部门
        getAllChildDepartments(deptId, allDeptIds);
        
        // 获取所有部门下的筒仓ID
        List<Integer> result = new ArrayList<>();
        for (Integer departmentId : allDeptIds) {
            List<Integer> rlIds = rlPermissionService.getRlIdsByDeptId(departmentId);
            if (rlIds != null && !rlIds.isEmpty()) {
                result.addAll(rlIds);
            }
        }
        
        // 去重并返回
        return new ArrayList<>(result.stream().distinct().collect(java.util.stream.Collectors.toList()));
    }
    
    /**
     * 递归获取所有子部门ID
     * @param parentId 父部门ID
     * @param result 结果集合
     */
    private void getAllChildDepartments(Integer parentId, List<Integer> result) {
        ResultVO resultVO = departmentClient.getDepartmentListByParentId(parentId);
        if (resultVO != null && resultVO.getData() != null) {
            List<DepartmentDTO> children = (List<DepartmentDTO>) resultVO.getData();
            for (DepartmentDTO child : children) {
                result.add(child.getId());
                getAllChildDepartments(child.getId(), result);
            }
        }
    }

    @ApiOperation(value = "获取高粱仓库可视化数据")
    @RequestMapping(value = "/warehouse/sorghum", method = RequestMethod.GET)
    public ResultVO<WarehouseVisualizationDTO> getSorghumWarehouseVisualization() {
        WarehouseVisualizationDTO data = dataGenerator.generateSorghumVisualizationData();
        return ResultVO.success(data);
    }

    @ApiOperation(value = "获取稻壳仓库可视化数据")
    @RequestMapping(value = "/warehouse/rice-husk", method = RequestMethod.GET)
    public ResultVO<WarehouseVisualizationDTO> getRiceHuskWarehouseVisualization() {
        WarehouseVisualizationDTO data = dataGenerator.generateRiceHuskVisualizationData();
        return ResultVO.success(data);
    }

    @ApiOperation(value = "获取小麦仓库可视化数据")
    @RequestMapping(value = "/warehouse/wheat", method = RequestMethod.GET)
    public ResultVO<WarehouseVisualizationDTO> getWheatWarehouseVisualization() {
        WarehouseVisualizationDTO data = dataGenerator.generateWheatVisualizationData();
        return ResultVO.success(data);
    }
}