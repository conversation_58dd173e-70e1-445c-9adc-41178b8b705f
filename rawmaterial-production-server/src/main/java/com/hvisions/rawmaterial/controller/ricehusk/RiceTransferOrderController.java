package com.hvisions.rawmaterial.controller.ricehusk;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.rawmaterial.dto.production.rice.transfer.detail.RiceTransferDetailListDTO;
import com.hvisions.rawmaterial.dto.production.rice.transfer.order.RiceTransferOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.rice.transfer.order.RiceTransferOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailUpdateDTO;
import com.hvisions.rawmaterial.service.RiceTransferOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description:稻壳转运工单
 * @date 2022/4/22 10:18
 */
@RestController
@RequestMapping(value = "/rice/transfer/order")
@Api(tags = "稻壳转运工单")
public class RiceTransferOrderController {

    @Resource
    private RiceTransferOrderService branTransferOrderService;


    @ApiOperation(value = "分页查询稻壳转运工单")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<RiceTransferOrderPageDTO> getRiceTransferOrderPageList(@RequestBody RiceTransferOrderPageQueryDTO queryDTO) {
        return branTransferOrderService.getRiceTransferOrderPageList(queryDTO);
    }

    @ApiOperation(value = "批量新增稻壳转运工单详情")
    @RequestMapping(value = "/detail/insert/batch", method = RequestMethod.POST)
    public Integer insertOrderDetail(@RequestBody List<DetailInsertDTO> detailInsertDTOS) {
        return branTransferOrderService.insertOrderDetail(detailInsertDTOS);
    }

    @ApiOperation(value = "修改稻壳转运工单详情")
    @RequestMapping(value = "/detailUpdate", method = RequestMethod.POST)
    public Integer detailUpdate(@RequestBody DetailUpdateDTO updateDTO) {
        return branTransferOrderService.detailUpdate(updateDTO);
    }

    @ApiOperation(value = "分页查询稻壳转运工单详细")
    @RequestMapping(value = "/getRiceTransferOrderDetailPageList", method = RequestMethod.POST)
    public Page<RiceTransferDetailListDTO> getRiceTransferOrderDetailPageList(@RequestBody RiceTransferOrderPageQueryDTO queryDTO) {
        return branTransferOrderService.getRiceTransferOrderDetailPageList(queryDTO);
    }

    @ApiOperation("从中控同步稻壳转运工单")
    @PostMapping("/sync")
    public ResultVO<String> syncFromCentralControl() {
        try {
            String result = branTransferOrderService.syncFromCentralControl();
            return ResultVO.success(result);
        } catch (Exception e) {
            throw new BaseKnownException(10000, "同步稻壳转运工单记录失败：" + e.getMessage());
        }
    }
}
