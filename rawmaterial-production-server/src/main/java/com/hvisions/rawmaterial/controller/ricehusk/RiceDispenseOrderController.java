package com.hvisions.rawmaterial.controller.ricehusk;

import com.hvisions.rawmaterial.dto.production.rice.dispense.detail.RiceDispenseDetailListDTO;
import com.hvisions.rawmaterial.dto.production.rice.dispense.order.RiceDispenseOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.rice.dispense.order.RiceDispenseOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.service.RiceDispenseOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description:稻壳分发工单-二期改为清蒸进料任务
 * @date 2022/4/22 10:18
 */
@RestController
@RequestMapping(value = "/rice/dispense/order")
@Api(tags = "稻壳分发工单-二期改为清蒸进料任务")
public class RiceDispenseOrderController {

    @Resource
    private RiceDispenseOrderService riceDispenseOrderService;


    @ApiOperation(value = "分页查询稻壳分发工单")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<RiceDispenseOrderPageDTO> getRiceDispenseOrderPageList(@RequestBody RiceDispenseOrderPageQueryDTO queryDTO) {
        return riceDispenseOrderService.getRiceDispenseOrderPageList(queryDTO);
    }

    @ApiOperation(value = "批量新增稻壳分发工单详情")
    @RequestMapping(value = "/detail/insert/batch", method = RequestMethod.POST)
    public Integer insertOrderDetail(@RequestBody List<DetailInsertDTO> detailInsertDTOS) {
        return riceDispenseOrderService.insertOrderDetail(detailInsertDTOS);
    }

    @ApiOperation(value = "分页查询稻壳分发工单详细")
    @RequestMapping(value = "/getRiceDispenseOrderDetailPageList", method = RequestMethod.POST)
    public Page<RiceDispenseDetailListDTO> getRiceDispenseOrderDetailPageList(@RequestBody RiceDispenseOrderPageQueryDTO queryDTO) {
        return riceDispenseOrderService.getRiceDispenseOrderDetailPageList(queryDTO);
    }

}
