package com.hvisions.rawmaterial.controller.sorghum;

import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.SorghumAndBranBatchPostDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.dispense.detail.MaterialDispenseDetailDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.dispense.detail.MaterialDispenseDetailQueryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.dispense.detail.SorghumDispenseDetailListDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.dispense.order.SorghumDispenseOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.dispense.order.SorghumDispenseOrderPageQueryDTO;
import com.hvisions.rawmaterial.service.SorghumDispenseOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱分发工单-二期改为碎料发放任务
 * @date 2022/4/22 10:18
 */
@RestController
@RequestMapping(value = "/sorghum/dispense/order")
@Api(tags = "高粱分发工单-二期改为碎料发放任务")
public class SorghumDispenseOrderController {

    @Resource
    private SorghumDispenseOrderService sorghumDispenseOrderService;


    @ApiOperation(value = "分页查询高粱分发工单")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<SorghumDispenseOrderPageDTO> getSorghumDispenseOrderPageList(@RequestBody SorghumDispenseOrderPageQueryDTO queryDTO) {
        return sorghumDispenseOrderService.getSorghumDispenseOrderPageList(queryDTO);
    }

    @ApiOperation(value = "批量新增高粱分发工单详情")
    @RequestMapping(value = "/detail/insert/batch", method = RequestMethod.POST)
    public Integer insertOrderDetail(@RequestBody List<DetailInsertDTO> detailInsertDTOS) {
        return sorghumDispenseOrderService.insertOrderDetail(detailInsertDTOS);
    }


    @ApiOperation(value = "高粱粉发放冲销")
    @RequestMapping(value = "/write/off", method = RequestMethod.POST)
    public Integer writeOff(@RequestBody List<String> matDocs) {
        return sorghumDispenseOrderService.writeOff(matDocs);
    }

    @ApiOperation(value = "高粱粉发放过账")
    @RequestMapping(value = "/posting", method = RequestMethod.POST)
    public Integer posting(@RequestBody SorghumAndBranBatchPostDTO postDTO) {
        return sorghumDispenseOrderService.posting(postDTO);
    }

    /***
     * @Description 高粱稻壳发放详情（批次追溯）
     *
     * <AUTHOR>
     * @Date 2022-11-7 15:55
     * @param queryDTO
     * @return com.hvisions.purchase.dto.production.sorghum.dispense.detail.MaterialDispenseDetailDTO
     **/
    @ApiOperation(value = "高粱稻壳发放详情（批次追溯）")
    @RequestMapping(value = "/material/dispense/get", method = RequestMethod.POST)
    public List<MaterialDispenseDetailDTO> getMaterialDispenseDetail(@RequestBody MaterialDispenseDetailQueryDTO queryDTO) {
        return sorghumDispenseOrderService.getMaterialDispenseDetail(queryDTO);
    }

    @ApiOperation(value = "撤销自动同步")
    @RequestMapping(value = "/revoke", method = RequestMethod.POST)
    public Integer revoke(@RequestBody List<Integer> ids) {
        return sorghumDispenseOrderService.revoke(ids);
    }


    @ApiOperation(value = "分页查询高粱分发工单详细")
    @RequestMapping(value = "/getSorghumDispenseOrderDetailPageList", method = RequestMethod.POST)
    public Page<SorghumDispenseDetailListDTO> getSorghumDispenseOrderDetailPageList(@RequestBody SorghumDispenseOrderPageQueryDTO queryDTO) {
        return sorghumDispenseOrderService.getSorghumDispenseOrderDetailPageList(queryDTO);
    }
}
