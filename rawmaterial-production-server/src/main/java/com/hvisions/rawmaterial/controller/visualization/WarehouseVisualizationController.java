package com.hvisions.rawmaterial.controller.visualization;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.rawmaterial.dto.visualization.WarehouseVisualizationDTO;
import com.hvisions.rawmaterial.utils.WarehouseVisualizationDataGenerator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 原辅料仓库可视化控制器
 * 
 * <AUTHOR>
 * @date 2025-09-15
 */
@RestController
@RequestMapping(value = "/raw-material/warehouse-visualization")
@Api(tags = "原辅料仓库可视化")
public class WarehouseVisualizationController {

    @Resource
    private WarehouseVisualizationDataGenerator dataGenerator;

    @ApiOperation(value = "获取高粱仓库可视化数据", notes = "返回高粱原辅料仓库的完整可视化数据，包括原辅料仓库、前处理储存仓、后处理暂存仓和车间信息")
    @RequestMapping(value = "/sorghum", method = RequestMethod.GET)
    public ResultVO<WarehouseVisualizationDTO> getSorghumWarehouseVisualization() {
        try {
            WarehouseVisualizationDTO data = dataGenerator.generateSorghumVisualizationData();
            return ResultVO.success(data);
        } catch (Exception e) {
            return ResultVO.error("获取高粱仓库可视化数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取稻壳仓库可视化数据", notes = "返回稻壳原辅料仓库的完整可视化数据，包括原辅料仓库、前处理储存仓、后处理暂存仓和车间信息")
    @RequestMapping(value = "/rice-husk", method = RequestMethod.GET)
    public ResultVO<WarehouseVisualizationDTO> getRiceHuskWarehouseVisualization() {
        try {
            WarehouseVisualizationDTO data = dataGenerator.generateRiceHuskVisualizationData();
            return ResultVO.success(data);
        } catch (Exception e) {
            return ResultVO.error("获取稻壳仓库可视化数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取小麦仓库可视化数据", notes = "返回小麦原辅料仓库的完整可视化数据，包括原辅料仓库、前处理储存仓、后处理暂存仓和车间信息")
    @RequestMapping(value = "/wheat", method = RequestMethod.GET)
    public ResultVO<WarehouseVisualizationDTO> getWheatWarehouseVisualization() {
        try {
            WarehouseVisualizationDTO data = dataGenerator.generateWheatVisualizationData();
            return ResultVO.success(data);
        } catch (Exception e) {
            return ResultVO.error("获取小麦仓库可视化数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取所有物料类型的仓库可视化数据", notes = "返回所有物料类型的仓库可视化数据概览")
    @RequestMapping(value = "/all", method = RequestMethod.GET)
    public ResultVO<Object> getAllWarehouseVisualization() {
        try {
            // 创建包含所有物料类型数据的响应对象
            java.util.Map<String, WarehouseVisualizationDTO> allData = new java.util.HashMap<>();
            allData.put("sorghum", dataGenerator.generateSorghumVisualizationData());
            allData.put("riceHusk", dataGenerator.generateRiceHuskVisualizationData());
            allData.put("wheat", dataGenerator.generateWheatVisualizationData());
            
            return ResultVO.success(allData);
        } catch (Exception e) {
            return ResultVO.error("获取所有仓库可视化数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取仓库可视化数据概览", notes = "返回各物料类型的库存概览信息")
    @RequestMapping(value = "/overview", method = RequestMethod.GET)
    public ResultVO<Object> getWarehouseOverview() {
        try {
            java.util.Map<String, Object> overview = new java.util.HashMap<>();
            
            // 高粱概览
            WarehouseVisualizationDTO sorghumData = dataGenerator.generateSorghumVisualizationData();
            java.util.Map<String, Object> sorghumOverview = new java.util.HashMap<>();
            sorghumOverview.put("materialType", "高粱");
            sorghumOverview.put("totalStock", sorghumData.getRawMaterialWarehouse().getTotalStock());
            sorghumOverview.put("preProcessingStock", sorghumData.getPreProcessingStorage().getTotalStorage());
            sorghumOverview.put("postProcessingStock", sorghumData.getPostProcessingStorage().getTotalStorage());
            overview.put("sorghum", sorghumOverview);
            
            // 稻壳概览
            WarehouseVisualizationDTO riceHuskData = dataGenerator.generateRiceHuskVisualizationData();
            java.util.Map<String, Object> riceHuskOverview = new java.util.HashMap<>();
            riceHuskOverview.put("materialType", "稻壳");
            riceHuskOverview.put("totalStock", riceHuskData.getRawMaterialWarehouse().getTotalStock());
            riceHuskOverview.put("preProcessingStock", riceHuskData.getPreProcessingStorage().getTotalStorage());
            riceHuskOverview.put("postProcessingStock", riceHuskData.getPostProcessingStorage().getTotalStorage());
            overview.put("riceHusk", riceHuskOverview);
            
            // 小麦概览
            WarehouseVisualizationDTO wheatData = dataGenerator.generateWheatVisualizationData();
            java.util.Map<String, Object> wheatOverview = new java.util.HashMap<>();
            wheatOverview.put("materialType", "小麦");
            wheatOverview.put("totalStock", wheatData.getRawMaterialWarehouse().getTotalStock());
            wheatOverview.put("preProcessingStock", wheatData.getPreProcessingStorage().getTotalStorage());
            wheatOverview.put("postProcessingStock", wheatData.getPostProcessingStorage().getTotalStorage());
            overview.put("wheat", wheatOverview);
            
            return ResultVO.success(overview);
        } catch (Exception e) {
            return ResultVO.error("获取仓库概览数据失败: " + e.getMessage());
        }
    }
}
