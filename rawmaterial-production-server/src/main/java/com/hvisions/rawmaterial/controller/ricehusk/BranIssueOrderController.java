package com.hvisions.rawmaterial.controller.ricehusk;

import com.hvisions.rawmaterial.dto.production.bran.issue.detail.BranIssueDetailListDTO;
import com.hvisions.rawmaterial.dto.production.bran.issue.order.BranIssueOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.bran.issue.order.BranIssueOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.SorghumAndBranBatchPostDTO;
import com.hvisions.rawmaterial.service.BranIssueOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description:熟糠发放工单-二期熟稻壳发放任务
 * @date 2022/4/22 10:18
 */
@RestController
@RequestMapping(value = "/bran/issue/order")
@Api(tags = "熟糠发放工单")
public class BranIssueOrderController {

    @Resource
    private BranIssueOrderService branIssueOrderService;


    @ApiOperation(value = "分页查询熟糠发放工单")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<BranIssueOrderPageDTO> getBranIssueOrderPageList(@RequestBody BranIssueOrderPageQueryDTO queryDTO) {
        return branIssueOrderService.getBranIssueOrderPageList(queryDTO);
    }

    @ApiOperation(value = "批量新增熟糠发放工单详情")
    @RequestMapping(value = "/detail/insert/batch", method = RequestMethod.POST)
    public Integer insertOrderDetail(@RequestBody List<DetailInsertDTO> detailInsertDTOS) {
        return branIssueOrderService.insertOrderDetail(detailInsertDTOS);
    }

    @ApiOperation(value = "熟糠粉发放冲销")
    @RequestMapping(value = "/write/off", method = RequestMethod.POST)
    public Integer writeOff(@RequestBody List<String> matDocs) {
        return branIssueOrderService.writeOff(matDocs);
    }

    @ApiOperation(value = "熟糠发放过账")
    @RequestMapping(value = "/posting", method = RequestMethod.POST)
    public Integer posting(@RequestBody SorghumAndBranBatchPostDTO postingDTO) {
        return branIssueOrderService.posting(postingDTO);
    }

    @ApiOperation(value = "撤销自动同步")
    @RequestMapping(value = "/revoke", method = RequestMethod.POST)
    public Integer revoke(@RequestBody List<Integer> ids) {
        return branIssueOrderService.revoke(ids);
    }

    @ApiOperation(value = "分页查询熟糠发放工单详情")
    @RequestMapping(value = "/getBranIssueOrderDetailPageList", method = RequestMethod.POST)
    public Page<BranIssueDetailListDTO> getBranIssueOrderDetailPageList(@RequestBody BranIssueOrderPageQueryDTO queryDTO) {
        return branIssueOrderService.getBranIssueOrderDetailPageList(queryDTO);
    }
}
