package com.hvisions.rawmaterial.controller.sorghum;

import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.production.detail.SorghumProductionDetailListDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.production.order.SorghumProductionOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.production.order.SorghumProductionOrderPageQueryDTO;
import com.hvisions.rawmaterial.service.SorghumProductionOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱生产工单-二期改为破碎任务
 * @date 2022/4/22 10:18
 */
@RestController
@RequestMapping(value = "/sorghum/production/order")
@Api(tags = "高粱生产工单")
public class SorghumProductionOrderController {

    @Resource
    private SorghumProductionOrderService sorghumProductionOrderService;


    @ApiOperation(value = "分页查询高粱生产工单")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<SorghumProductionOrderPageDTO> getSorghumProductionOrderPageList(@RequestBody SorghumProductionOrderPageQueryDTO queryDTO) {
        return sorghumProductionOrderService.getSorghumProductionOrderPageList(queryDTO);
    }

    @ApiOperation(value = "批量新增高粱生产工单详情")
    @RequestMapping(value = "/detail/insert/batch", method = RequestMethod.POST)
    public Integer insertOrderDetail(@RequestBody List<DetailInsertDTO> detailInsertDTOS) {
        return sorghumProductionOrderService.insertOrderDetail(detailInsertDTOS);
    }

    @ApiOperation(value = "分页查询高粱生产工单详情")
    @RequestMapping(value = "/getSorghumProductionOrderDetailPageList", method = RequestMethod.POST)
    public Page<SorghumProductionDetailListDTO> getSorghumProductionOrderDetailPageList(@RequestBody SorghumProductionOrderPageQueryDTO queryDTO) {
        return sorghumProductionOrderService.getSorghumProductionOrderDetailPageList(queryDTO);
    }
}
