package com.hvisions.rawmaterial.controller.sorghum;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailUpdateDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.transfer.detail.SorghumTransferDetailListDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.transfer.order.SorghumTransferOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.transfer.order.SorghumTransferOrderPageQueryDTO;
import com.hvisions.rawmaterial.service.sorghum.SorghumTransferOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱转运工单
 * @date 2022/4/22 10:18
 */
@RestController
@RequestMapping(value = "/sorghum/transfer/order")
@Api(tags = "高粱转运工单")
public class SorghumTransferOrderController {

    @Resource
    private SorghumTransferOrderService branTransferOrderService;


    @ApiOperation(value = "分页查询高粱转运工单")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<SorghumTransferOrderPageDTO> getSorghumTransferOrderPageList(@RequestBody SorghumTransferOrderPageQueryDTO queryDTO) {
        return branTransferOrderService.getSorghumTransferOrderPageList(queryDTO);
    }

    @ApiOperation(value = "批量新增高粱转运工单详情")
    @RequestMapping(value = "/detail/insert/batch", method = RequestMethod.POST)
    public Integer insertOrderDetail(@RequestBody List<DetailInsertDTO> detailInsertDTOS) {
        return branTransferOrderService.insertOrderDetail(detailInsertDTOS);
    }

    @ApiOperation(value = "修改高粱转运工单详情")
    @RequestMapping(value = "/detailUpdate", method = RequestMethod.POST)
    public Integer detailUpdate(@RequestBody DetailUpdateDTO updateDTO) {
        return branTransferOrderService.detailUpdate(updateDTO);
    }

    @ApiOperation(value = "分页查询高粱转运工单详细")
    @RequestMapping(value = "/getSorghumTransferOrderDetailPageList", method = RequestMethod.POST)
    public Page<SorghumTransferDetailListDTO> getSorghumTransferOrderDetailPageList(@RequestBody SorghumTransferOrderPageQueryDTO queryDTO) {
        return branTransferOrderService.getSorghumTransferOrderDetailPageList(queryDTO);
    }

    @ApiOperation("从中控同步入仓记录")
    @PostMapping("/sync")
    public ResultVO<String> syncFromCentralControl(@RequestBody List<DetailInsertDTO> detailInsertDTOS) {
        try {
            String result = branTransferOrderService.syncFromCentralControl(detailInsertDTOS);
            return ResultVO.success(result);
        } catch (Exception e) {
            throw new BaseKnownException(10000, "同步入仓记录失败：" + e.getMessage());
        }
    }
}
