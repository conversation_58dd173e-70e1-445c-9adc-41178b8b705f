package com.hvisions.rawmaterial.controller.sorghum;

import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.detail.SorghumShipmentDetailListDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.order.SorghumShipmentOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.order.SorghumShipmentOrderPageQueryDTO;
import com.hvisions.rawmaterial.service.SorghumShipmentOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱转粮工单- 二期改为发料任务
 * @date 2022/4/22 10:18
 */
@RestController
@RequestMapping(value = "/sorghum/shipment/order")
@Api(tags = "高粱转粮工单-二期改为发料任务")
public class SorghumShipmentOrderController {

    @Resource
    private SorghumShipmentOrderService sorghumShipmentOrderService;


    @ApiOperation(value = "分页查询高粱转粮工单")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<SorghumShipmentOrderPageDTO> getSorghumShipmentOrderPageList(@RequestBody SorghumShipmentOrderPageQueryDTO queryDTO) {
        return sorghumShipmentOrderService.getSorghumShipmentOrderPageList(queryDTO);
    }

    @ApiOperation(value = "批量新增高粱转粮工单详情")
    @RequestMapping(value = "/detail/insert/batch", method = RequestMethod.POST)
    public Integer insertOrderDetail(@RequestBody List<DetailInsertDTO> detailInsertDTOS) {
        return sorghumShipmentOrderService.insertOrderDetail(detailInsertDTOS);
    }

    @ApiOperation(value = "分页查询高粱转粮工单详情")
    @RequestMapping(value = "/getSorghumShipmentOrderDetailPageList", method = RequestMethod.POST)
    public Page<SorghumShipmentDetailListDTO> getSorghumShipmentOrderDetailPageList(@RequestBody SorghumShipmentOrderPageQueryDTO queryDTO) {
        return sorghumShipmentOrderService.getSorghumShipmentOrderDetailPageList(queryDTO);
    }
}
