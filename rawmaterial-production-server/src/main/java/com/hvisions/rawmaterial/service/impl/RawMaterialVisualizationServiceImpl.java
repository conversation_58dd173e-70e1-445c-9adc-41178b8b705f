package com.hvisions.rawmaterial.service.impl;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.rawmaterial.dao.RawMaterialVisualizationMapper;
import com.hvisions.rawmaterial.dto.visualization.SiloVisualizationDTO;
import com.hvisions.rawmaterial.dto.visualization.SiloVisualizationQueryDTO;
import com.hvisions.rawmaterial.service.RawMaterialVisualizationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 原辅料可视化服务实现类
 * @author: z19235
 * @time: 2023/07/10
 */
@Service
public class RawMaterialVisualizationServiceImpl implements RawMaterialVisualizationService {

    @Resource
    private RawMaterialVisualizationMapper rawMaterialVisualizationMapper;

    @Override
    public List<SiloVisualizationDTO> getSiloVisualization(SiloVisualizationQueryDTO queryDTO) {
        if (queryDTO.getMaterialType() == null) {
            throw new BaseKnownException(10000, "物料类型不能为空");
        }
        
        // 使用Mapper直接查询筒仓可视化数据
        List<SiloVisualizationDTO> siloList = rawMaterialVisualizationMapper.getSiloVisualization(queryDTO);
        
        // 如果需要构建树形结构，则使用层级查询
        List<SiloVisualizationDTO> hierarchyList = rawMaterialVisualizationMapper.getSiloHierarchy(queryDTO.getMaterialType(), queryDTO.getRlIds());
        
        // 构建树形结构
        return buildSiloTree(hierarchyList);
    }
    
    /**
     * 构建筒仓树形结构
     * @param siloList 筒仓列表
     * @return 树形结构的筒仓列表
     */
    private List<SiloVisualizationDTO> buildSiloTree(List<SiloVisualizationDTO> siloList) {
        List<SiloVisualizationDTO> resultList = new ArrayList<>();
        Map<Integer, SiloVisualizationDTO> siloMap = new HashMap<>();
        
        // 将所有筒仓放入Map中，方便查找
        for (SiloVisualizationDTO silo : siloList) {
            siloMap.put(silo.getId(), silo);
            silo.setChildren(new ArrayList<>());
        }
        
        // 构建树形结构
        for (SiloVisualizationDTO silo : siloList) {
            // 如果是根节点，则添加到结果列表中
            if (silo.getParentId() == null) {
                resultList.add(silo);
            } else {
                // 如果不是根节点，则添加到父节点的子节点列表中
                SiloVisualizationDTO parent = siloMap.get(silo.getParentId());
                if (parent != null) {
                    parent.getChildren().add(silo);
                }
            }
        }
        
        return resultList;
    }
}