package com.hvisions.rawmaterial.service;

import com.hvisions.rawmaterial.dto.visualization.SiloVisualizationDTO;
import com.hvisions.rawmaterial.dto.visualization.SiloVisualizationQueryDTO;

import java.util.List;

/**
 * @Description: 原辅料可视化服务接口
 * @author: z19235
 * @time: 2023/07/10
 */
public interface RawMaterialVisualizationService {

    /**
     * 获取筒仓可视化数据
     * @param queryDTO 查询参数
     * @return 筒仓可视化数据列表
     */
    List<SiloVisualizationDTO> getSiloVisualization(SiloVisualizationQueryDTO queryDTO);
}