package com.hvisions.rawmaterial.service.materialsilo;

import com.hvisions.rawmaterial.entity.materialsilo.RlPermission;
import java.util.List;

/**
 * @Description: 筒仓数据权限服务接口
 * @author: z19235
 */
public interface RlPermissionService {

    /**
     * 保存筒仓权限配置
     * @param permissionList 权限配置列表
     * @return 影响行数
     */
    Integer saveRlPermissions(List<RlPermission> permissionList);

    /**
     * 根据筒仓ID删除权限配置
     * @param rlId 筒仓ID
     * @return 影响行数
     */
    Integer deleteByRlId(Integer rlId);

    /**
     * 根据筒仓ID查询权限配置
     * @param rlId 筒仓ID
     * @return 权限配置列表
     */
    List<RlPermission> getByRlId(Integer rlId);

    /**
     * 根据部门ID查询有权限的筒仓ID列表
     * @param deptId 部门ID
     * @return 筒仓ID列表
     */
    List<Integer> getRlIdsByDeptId(Integer deptId);
}