package com.hvisions.rawmaterial.entity.ricehusk;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.rawmaterial.entity.SysBase;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 稻壳入仓任务实体
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Table(name = "t_mpd_rice_husk_inbound_task")
@org.hibernate.annotations.Table(appliesTo = "t_mpd_rice_husk_inbound_task", comment = "稻壳入仓任务")
@Entity
@TableName("t_mpd_rice_husk_inbound_task")
@KeySequence("t_mpd_rice_husk_inbound_task_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TMpdRiceHuskInboundTask extends SysBase {

    /**
     * 工单号
     */
    @Column(name = "order_no", length = 20, columnDefinition = "varchar(20) COMMENT '工单号'")
    private String orderNo;

    /**
     * 工单日期
     */
    @Column(name = "order_date", columnDefinition = "datetime COMMENT '工单日期'")
    private Date orderDate;

    /**
     * 物料 id
     */
    @Column(name = "material_id", length = 50,columnDefinition = "int COMMENT '物料id'")
    private Integer materialId;

    /**
     * 物料名称
     */
    @Column(name = "material_name", length = 50, columnDefinition = "varchar(50) COMMENT '物料名称'")
    private String materialName;

    /**
     * 物料编码
     */
    @Column(name = "material_code", length = 20, columnDefinition = "varchar(20) COMMENT '物料编码'")
    private String materialCode;

    /**
     * 数量
     */
    @Column(name = "quantity", precision = 20, scale = 6, columnDefinition = "decimal(20,6) COMMENT '数量'")
    private BigDecimal quantity;

    /**
     * 单位
     */
    @Column(name = "unit", length = 10, columnDefinition = "varchar(10) COMMENT '单位'")
    private String unit;

    /**
     * 备注
     */
    @Column(name = "remark", length = 200, columnDefinition = "varchar(200) COMMENT '备注'")
    private String remark;
}
