<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.dao.RawMaterialVisualizationMapper">

    <!-- 获取筒仓可视化数据 -->
    <select id="getSiloVisualization" resultType="com.hvisions.rawmaterial.dto.visualization.SiloVisualizationDTO">
        SELECT
            r.id,
            r.code,
            r.name,
            r.safety_stock as safetyStock,
            r.volume,
            r.unit,
            r.material_type as materialType,
            w.material_code as materialCode,
            SUM(w.stock_quantity) as stockQuantity,
            CASE
                WHEN EXISTS (
                    SELECT 1 FROM t_mpd_applicable_order o
                    WHERE (o.in_reservoir_id = r.id OR o.out_reservoir_id = r.id)
                    AND o.status IN (0, 1) -- 假设0表示进行中，1表示已下发
                ) THEN
                    CASE
                        WHEN EXISTS (
                            SELECT 1 FROM t_mpd_applicable_order o
                            WHERE o.in_reservoir_id = r.id
                            AND o.status IN (0, 1)
                        ) THEN 1 -- 入仓中
                        ELSE 2 -- 出仓中
                    END
                ELSE 0 -- 空闲
            END as status,
            CASE
                WHEN r.code LIKE 'YFL%' THEN 0 -- 原辅料段
                WHEN r.code LIKE 'ZX%' THEN 1 -- 中心现场段
                ELSE 0 -- 默认为原辅料段
            END as areaType
        FROM
            t_mpd_rl_management r
        LEFT JOIN
            t_mpd_warehouse_data w ON r.id = w.storage_id AND w.deleted = 0
        WHERE
            r.deleted = 0
            AND r.material_type = #{queryDTO.materialType}
            <if test="queryDTO.rlIds != null and queryDTO.rlIds.size() > 0">
                AND r.id IN
                <foreach collection="queryDTO.rlIds" item="rlId" open="(" separator="," close=")">
                    #{rlId}
                </foreach>
            </if>
            <if test="queryDTO.areaType != null">
                AND (
                    CASE
                        WHEN r.code LIKE 'YFL%' THEN 0 -- 原辅料段
                        WHEN r.code LIKE 'ZX%' THEN 1 -- 中心现场段
                        ELSE 0 -- 默认为原辅料段
                    END
                ) = #{queryDTO.areaType}
            </if>
        GROUP BY
            r.id, r.code, r.name, r.safety_stock, r.volume, r.unit, r.material_type, w.material_code
        ORDER BY
            r.code
    </select>

    <!-- 获取筒仓层级结构 -->
    <select id="getSiloHierarchy" resultType="com.hvisions.rawmaterial.dto.visualization.SiloVisualizationDTO">
        WITH RECURSIVE silo_hierarchy AS (
            -- 查询根节点（没有父节点的筒仓）
            SELECT
                r.id,
                r.code,
                r.name,
                r.parent_id as parentId,
                r.safety_stock as safetyStock,
                r.volume,
                r.unit,
                r.material_type as materialType,
                0 as level
            FROM
                t_mpd_rl_management r
            WHERE
                r.deleted = 0
                AND r.material_type = #{materialType}
                AND r.parent_id IS NULL
                <if test="rlIds != null and rlIds.size() > 0">
                    AND r.id IN
                    <foreach collection="rlIds" item="rlId" open="(" separator="," close=")">
                        #{rlId}
                    </foreach>
                </if>
            
            UNION ALL
            
            -- 递归查询子节点
            SELECT
                r.id,
                r.code,
                r.name,
                r.parent_id as parentId,
                r.safety_stock as safetyStock,
                r.volume,
                r.unit,
                r.material_type as materialType,
                h.level + 1
            FROM
                t_mpd_rl_management r
            JOIN
                silo_hierarchy h ON r.parent_id = h.id
            WHERE
                r.deleted = 0
                AND r.material_type = #{materialType}
                <if test="rlIds != null and rlIds.size() > 0">
                    AND r.id IN
                    <foreach collection="rlIds" item="rlId" open="(" separator="," close=")">
                        #{rlId}
                    </foreach>
                </if>
        )
        SELECT
            h.id,
            h.code,
            h.name,
            h.parentId,
            h.safetyStock,
            h.volume,
            h.unit,
            h.materialType,
            h.level,
            w.material_code as materialCode,
            SUM(w.stock_quantity) as stockQuantity,
            CASE
                WHEN EXISTS (
                    SELECT 1 FROM t_mpd_applicable_order o
                    WHERE (o.in_reservoir_id = h.id OR o.out_reservoir_id = h.id)
                    AND o.status IN (0, 1) -- 假设0表示进行中，1表示已下发
                ) THEN
                    CASE
                        WHEN EXISTS (
                            SELECT 1 FROM t_mpd_applicable_order o
                            WHERE o.in_reservoir_id = h.id
                            AND o.status IN (0, 1)
                        ) THEN 1 -- 入仓中
                        ELSE 2 -- 出仓中
                    END
                ELSE 0 -- 空闲
            END as status,
            CASE
                WHEN h.code LIKE 'YFL%' THEN 0 -- 原辅料段
                WHEN h.code LIKE 'ZX%' THEN 1 -- 中心现场段
                ELSE 0 -- 默认为原辅料段
            END as areaType
        FROM
            silo_hierarchy h
        LEFT JOIN
            t_mpd_warehouse_data w ON h.id = w.storage_id AND w.deleted = 0
        GROUP BY
            h.id, h.code, h.name, h.parentId, h.safetyStock, h.volume, h.unit, h.materialType, h.level, w.material_code
        ORDER BY
            h.level, h.code
    </select>

</mapper>