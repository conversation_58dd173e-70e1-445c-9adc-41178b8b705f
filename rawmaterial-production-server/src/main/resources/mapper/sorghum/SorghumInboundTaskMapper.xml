<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.dao.sorghum.SorghumInboundTaskMapper">

    <resultMap id="BaseResultMap" type="com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumInboundTask">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
        <result column="silo_id" jdbcType="VARCHAR" property="siloId" />
        <result column="material_id" jdbcType="VARCHAR" property="materialId" />
        <result column="material_name" jdbcType="VARCHAR" property="materialName" />
        <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
        <result column="quantity" jdbcType="DECIMAL" property="quantity" />
        <result column="unit" jdbcType="VARCHAR" property="unit" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
        <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
        <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
        <result column="deleted" jdbcType="BOOLEAN" property="deleted" />
    </resultMap>

    <resultMap id="orderMap" type="com.hvisions.rawmaterial.dto.sorghum.SorghumInboundTaskDTO">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
        <result column="silo_id" jdbcType="VARCHAR" property="siloId" />
        <result column="silo_name" jdbcType="VARCHAR" property="siloName" />
        <result column="silo_code" jdbcType="VARCHAR" property="siloCode" />
        <result column="material_name" jdbcType="VARCHAR" property="materialName" />
        <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
        <result column="quantity" jdbcType="DECIMAL" property="quantity" />
        <result column="unit" jdbcType="VARCHAR" property="unit" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <collection property="detailList"
                    column="id"
                    javaType="java.util.List" select="selectDetail"
        >
        </collection>
    </resultMap>

    <select id="selectByCondition" resultMap="orderMap">
        SELECT
        sit.id,sit.order_no,sit.order_date,sit.silo_id,sit.material_id,sit.material_name,sit.material_code,
        sit.quantity,sit.unit,sit.start_time,sit.end_time,sit.status,sit.remark,sit.create_time,sit.update_time,
        sit.creator_id,sit.updater_id
        ,ms.name silo_name,ms.code silo_code
        FROM t_mpd_sorghum_inbound_task sit
        left join t_mpd_material_silo ms ON sit.silo_id = ms.id
        WHERE sit.deleted = 0
        <if test="siloCode != null and siloCode != ''">
            AND sit.silo_code = #{siloCode}
        </if>
        <if test="status != null">
            AND sit.status = #{status}
        </if>
        <if test="orderDateStart != null">
            AND sit.order_date &gt;= #{orderDateStart}
        </if>
        <if test="orderDateEnd != null">
            AND sit.order_date &lt;= #{orderDateEnd}
        </if>
        ORDER BY sit.order_no DESC
    </select>

    <select id="selectMaxSerialNoByDate" resultType="java.lang.String">
        SELECT
            MAX(SUBSTRING(order_no, 9, 3)) AS max_serial_no
        FROM
            t_mpd_sorghum_inbound_task
        WHERE
            deleted = 0
            AND order_no LIKE CONCAT('RC', DATE_FORMAT(#{orderDate}, '%y%m%d'), '%')
    </select>

    <select id="selectByOrderDate" resultMap="BaseResultMap">
        SELECT
         *
        FROM t_mpd_sorghum_inbound_task
        WHERE deleted = 0
            AND DATE(order_date) = DATE(#{orderDate})
        LIMIT 1
    </select>

    <select id="selectByDateAndMaterialCode" resultMap="BaseResultMap">
        SELECT
        id, order_no, order_date, silo_id, material_id, material_name, material_code,
        quantity, unit, start_time, end_time, status, remark, create_time, update_time,
        creator_id, updater_id,site_num,deleted
        FROM t_mpd_sorghum_inbound_task
        WHERE deleted = 0
            AND DATE_FORMAT(order_date, '%Y%m%d') = #{dateStr}
            AND material_code = #{materialCode}
        LIMIT 1
    </select>

    <select id="selectDetail" resultType="com.hvisions.rawmaterial.dto.sorghum.SorghumInboundTaskDetailDTO">
        SELECT
            id, task_id, accept_silo_code AS siloCode, accept_silo_name as  siloName, material_name, material_code, unit, quantity,
            actual_start_time, actual_completion_time, status, creator_name, unique_id,
            create_time, update_time
        FROM t_mpd_sorghum_inbound_task_detail
        WHERE deleted = 0
            AND task_id = #{id}
        ORDER BY actual_start_time DESC
    </select>
</mapper>