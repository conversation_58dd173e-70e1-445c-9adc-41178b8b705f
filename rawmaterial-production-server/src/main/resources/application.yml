#默认公共配置
#开启feign的hystrix熔断功能
feign:
  hystrix:
    enabled: true
spring:
  # war包重复部署如果设置开启会有问题。jar包部署可以打开
  jmx:
    enabled: false
  cloud:
    refresh:
      #为了解决springboot与spring cloud数据库初始化检查添加的配置项的循环依赖问题所添加
      refreshable: none
  profiles:
    #默认配置为mysql配置
    active: mysql
    include: test
  http:
    encoding:
      force: true
    charset: UTF-8
    enabled: true
  tomcat:
    uri-encoding: UTF-8
  #使用redis作为缓存
  cache:
    type: redis
  #redis 地址和端口号
  redis:
    host: ************
    port: 6379
    password: 1001
    #password: XekUN4yN@N
    #cluster:
    #  nodes: ***********:7000,***********:7001
    #  password: XekUN4yN@N


  #支持消息队列
  rabbitmq:
    host: ************
    port: 5672
    username: admin
    password: 1001
    #addresses: ***********:5672
    #password: Np3@ZE6Q@S
    #username: admin
  #序列化时间格式
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  jpa:
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
    #是否打印Jpa生成的sql语句
    show-sql: true
    #数据库生成策略，如果打开会根据entity对象生成数据库。生产环境尽量不要使用
    hibernate:
      ddl-auto: update
  #服务注册名(此名称非常重要，是作为微服务框架中唯一标识一个服务的名称，相同名称的服务会被认为是提供一致接口的服务）
  application:
    name: rawmaterial-production-server
  #国际化配置
  messages:
    basename: i18n/messages
    cache-seconds: -1
    encoding: utf-8
  servlet:
    multipart:
      max-file-size: 100Mb
      max-request-size: 100Mb
ribbon:
  #请求处理的超时时间
  ReadTimeout: 120000
  #请求连接的超时时间
  ConnectTimeout: 30000
mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: deleted # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
      logic-delete-value: true # 逻辑已删除值
      logic-not-delete-value: false # 逻辑未删除值
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
mybatis:
  configuration:
    map-underscore-to-camel-case: true
  typeAliasesPackage: com.hvisions.rxwms.dto
  mapperLocations: classpath:mapper/*.xml
pagehelper:
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql
  page-size-zero: true
server:
  port: 9784
#开启所有的健康监控信息
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
#info标签：可以在springboot admin的Insights界面Detail中进行展示,也可以再eureka界面点击实例名称查看
info:
  build:
    artifact: '@project.artifactId@'
    version: '@project.version@'
    server-name: ${h-visions.service-name}
eureka:
  instance:
    prefer-ip-address: true
    #实例名
    instance-id: wms:${server.port}
    # 修改
  #    ip-address: ************
  #    ip-address: *************
  client:
    service-url:
      #euraka地址
      defaultZone: http://localhost:8763/eureka/
hystrix:
  command:
    default:  #default全局有效，service id指定应用有效
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 20000 #断路器超时时间，默认1000ms
h-visions:
  #是否添加所有控制器请求记录到log服务
  log:
    enable: false
  #服务名称,可以使用中文，日志服务会将这个字段传递
  service-name: rawmaterial-production-server
  # 此处配置为audit信息的创建方式。dto 为当请求控制器的时候如果入参为SysDTO可以自动赋值。jpa为使用jpa的audit方式进行实现。
  #可以使用swagger的接口，使用对应的测试方法，生成api文档，支持代码和ascii
  swagger:
    # 如果为false或者没有此属性。swagger界面将不会加载
    enable: true
    api-url: http://localhost:${server.port}/v2/api-docs;
    asciidoc-dir: ./build/asciidoc/
    markdown-dir: ./build/markdown/

material.type.da-qu: 大曲
material.type.sorghum: 高粱
material.type.rice-husk: 稻壳
material.type.back-alcoholic: 回酒
material.type.level-wine: 等级酒

material.name.zaopei: 糟醅
#material.name.base-wine: 基酒
material.name.base-wine-code: 30000132

scene.name.base-wine: 基酒检验
scene.name.in-pot: 入窖糟醅检验
scene.name.out-pot: 出窖糟醅检验

StandardTime.name.machine: 机器
StandardTime.name.power: 动力
StandardTime.name.other: 其他
StandardTime.name.auxiliary: 辅助
StandardTime.name.work-hours: 工时

#是否使用生产模块的sap
mkwine.isUseSap: true

#原辅料配置
purchase:
  schedule:
    sorghum:
      #高粱处理班组
      department-id: 85
      department-name: 高粱处理班组
    chaff:
      department-id: 31
      department-name: 稻壳处理班组