package com.hvisions.rawmaterial.service.ricehusk;

import com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskDTO;
import com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskQueryDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.test.context.ActiveProfiles;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 稻壳入仓任务服务测试类
 * 
 * <AUTHOR>
 * @date 2025-09-16
 */
@SpringBootTest
@ActiveProfiles("test")
public class RiceHuskInboundTaskServiceTest {

    @Autowired
    private RiceHuskInboundTaskService riceHuskInboundTaskService;

    @Test
    public void testGetRiceHuskInboundTaskPageList_BasicQuery() {
        // 测试基本查询
        RiceHuskInboundTaskQueryDTO queryDTO = new RiceHuskInboundTaskQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        
        Page<RiceHuskInboundTaskDTO> result = riceHuskInboundTaskService.getRiceHuskInboundTaskPageList(queryDTO);
        
        assertNotNull(result);
        assertTrue(result.getTotalElements() >= 0);
        System.out.println("基本查询结果数量: " + result.getTotalElements());
    }

    @Test
    public void testGetRiceHuskInboundTaskPageList_WithMainTableConditions() {
        // 测试主表条件查询
        RiceHuskInboundTaskQueryDTO queryDTO = new RiceHuskInboundTaskQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        queryDTO.setMaterialCode("11000544");
        queryDTO.setStatus(1);
        
        Page<RiceHuskInboundTaskDTO> result = riceHuskInboundTaskService.getRiceHuskInboundTaskPageList(queryDTO);
        
        assertNotNull(result);
        System.out.println("主表条件查询结果数量: " + result.getTotalElements());
        
        // 验证查询结果
        if (!result.getContent().isEmpty()) {
            RiceHuskInboundTaskDTO firstTask = result.getContent().get(0);
            assertEquals("11000544", firstTask.getMaterialCode());
            assertEquals(Integer.valueOf(1), firstTask.getStatus());
        }
    }

    @Test
    public void testGetRiceHuskInboundTaskPageList_WithDetailTableConditions() {
        // 测试子表条件查询
        RiceHuskInboundTaskQueryDTO queryDTO = new RiceHuskInboundTaskQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        queryDTO.setDetailSiloCode("DK2-1");
        queryDTO.setCreatorName("中控");
        
        Page<RiceHuskInboundTaskDTO> result = riceHuskInboundTaskService.getRiceHuskInboundTaskPageList(queryDTO);
        
        assertNotNull(result);
        System.out.println("子表条件查询结果数量: " + result.getTotalElements());
        
        // 验证查询结果包含详情数据
        if (!result.getContent().isEmpty()) {
            RiceHuskInboundTaskDTO firstTask = result.getContent().get(0);
            assertNotNull(firstTask.getDetailList());
            System.out.println("第一个任务的详情数量: " + firstTask.getDetailList().size());
        }
    }

    @Test
    public void testGetRiceHuskInboundTaskPageList_WithMixedConditions() {
        // 测试主表和子表混合条件查询
        RiceHuskInboundTaskQueryDTO queryDTO = new RiceHuskInboundTaskQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        // 主表条件
        queryDTO.setMaterialCode("11000544");
        queryDTO.setStatus(1);
        // 子表条件
        queryDTO.setDetailSiloCode("DK2-1");
        queryDTO.setDetailStatus(1);
        
        Page<RiceHuskInboundTaskDTO> result = riceHuskInboundTaskService.getRiceHuskInboundTaskPageList(queryDTO);
        
        assertNotNull(result);
        System.out.println("混合条件查询结果数量: " + result.getTotalElements());
    }

    @Test
    public void testGetRiceHuskInboundTaskPageList_WithDateRange() {
        // 测试日期范围查询
        RiceHuskInboundTaskQueryDTO queryDTO = new RiceHuskInboundTaskQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        
        // 设置工单日期范围
        Date startDate = new Date(System.currentTimeMillis() - 30L * 24 * 60 * 60 * 1000); // 30天前
        Date endDate = new Date(); // 现在
        queryDTO.setOrderDateStart(startDate);
        queryDTO.setOrderDateEnd(endDate);
        
        Page<RiceHuskInboundTaskDTO> result = riceHuskInboundTaskService.getRiceHuskInboundTaskPageList(queryDTO);
        
        assertNotNull(result);
        System.out.println("日期范围查询结果数量: " + result.getTotalElements());
    }

    @Test
    public void testGetRiceHuskInboundTaskPageList_WithUniqueId() {
        // 测试唯一标识查询
        RiceHuskInboundTaskQueryDTO queryDTO = new RiceHuskInboundTaskQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        queryDTO.setUniqueId("DK2-1_20250627001");
        
        Page<RiceHuskInboundTaskDTO> result = riceHuskInboundTaskService.getRiceHuskInboundTaskPageList(queryDTO);
        
        assertNotNull(result);
        System.out.println("唯一标识查询结果数量: " + result.getTotalElements());
    }
}
