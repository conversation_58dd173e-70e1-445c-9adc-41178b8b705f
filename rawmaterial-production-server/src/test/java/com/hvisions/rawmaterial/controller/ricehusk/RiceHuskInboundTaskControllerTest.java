package com.hvisions.rawmaterial.controller.ricehusk;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskQueryDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 稻壳入仓任务控制器测试类
 * 
 * <AUTHOR>
 * @date 2025-09-16
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
public class RiceHuskInboundTaskControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    public void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    public void testGetRiceHuskInboundTaskPageList_BasicQuery() throws Exception {
        setUp();
        
        RiceHuskInboundTaskQueryDTO queryDTO = new RiceHuskInboundTaskQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);

        mockMvc.perform(post("/ricehusk/inbound-task/list/page/get")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").exists())
                .andExpect(jsonPath("$.totalElements").exists());
    }

    @Test
    public void testGetRiceHuskInboundTaskPageList_WithMainTableConditions() throws Exception {
        setUp();
        
        RiceHuskInboundTaskQueryDTO queryDTO = new RiceHuskInboundTaskQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        queryDTO.setMaterialCode("11000544");
        queryDTO.setStatus(1);

        mockMvc.perform(post("/ricehusk/inbound-task/list/page/get")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").exists());
    }

    @Test
    public void testGetRiceHuskInboundTaskPageList_WithDetailTableConditions() throws Exception {
        setUp();
        
        RiceHuskInboundTaskQueryDTO queryDTO = new RiceHuskInboundTaskQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        queryDTO.setDetailSiloCode("DK2-1");
        queryDTO.setCreatorName("中控");

        mockMvc.perform(post("/ricehusk/inbound-task/list/page/get")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").exists());
    }

    @Test
    public void testGetRiceHuskInboundTaskPageList_WithMixedConditions() throws Exception {
        setUp();
        
        RiceHuskInboundTaskQueryDTO queryDTO = new RiceHuskInboundTaskQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        // 主表条件
        queryDTO.setMaterialCode("11000544");
        queryDTO.setStatus(1);
        // 子表条件
        queryDTO.setDetailSiloCode("DK2-1");
        queryDTO.setDetailStatus(1);

        mockMvc.perform(post("/ricehusk/inbound-task/list/page/get")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").exists());
    }

    @Test
    public void testGetRiceHuskInboundTaskPageList_WithUniqueId() throws Exception {
        setUp();
        
        RiceHuskInboundTaskQueryDTO queryDTO = new RiceHuskInboundTaskQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        queryDTO.setUniqueId("DK2-1_20250627001");

        mockMvc.perform(post("/ricehusk/inbound-task/list/page/get")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").exists());
    }
}
