# 稻壳入仓任务查询优化

## 概述

本次优化主要针对`RiceHuskInboundTaskController`的`getRiceHuskInboundTaskPageList`方法，实现了主子表联合查询功能，支持根据子表字段进行查询过滤。

## 优化内容

### 1. 扩展查询DTO

在`RiceHuskInboundTaskQueryDTO`中新增了以下子表查询字段：

- `detailSiloName`: 入仓名称（子表字段）
- `detailSiloCode`: 入仓编码（子表字段）
- `creatorName`: 创建人（子表字段）
- `actualStartTimeStart`: 实际开始时间开始（子表字段）
- `actualStartTimeEnd`: 实际开始时间结束（子表字段）
- `actualCompletionTimeStart`: 实际完成时间开始（子表字段）
- `actualCompletionTimeEnd`: 实际完成时间结束（子表字段）
- `detailStatus`: 详情状态（子表字段）
- `uniqueId`: 唯一标识（子表字段）

### 2. 优化Mapper查询

修改了`RiceHuskInboundTaskMapper.xml`中的`getRiceHuskInboundTaskPageList`查询：

- 使用`LEFT JOIN`连接主子表
- 添加`DISTINCT`避免重复数据
- 支持子表字段的查询条件
- 保持分页功能正常工作

### 3. 查询逻辑优化

- **智能JOIN**: 只有当查询条件包含子表字段时才进行LEFT JOIN，提高查询性能
- **条件过滤**: 支持主表和子表字段的混合查询条件
- **去重处理**: 使用DISTINCT确保主表记录不重复

## 使用示例

### 基本查询
```json
{
  "page": 1,
  "pageSize": 10
}
```

### 主表条件查询
```json
{
  "page": 1,
  "pageSize": 10,
  "materialCode": "11000544",
  "status": 1,
  "orderDateStart": "2025-06-01",
  "orderDateEnd": "2025-06-30"
}
```

### 子表条件查询
```json
{
  "page": 1,
  "pageSize": 10,
  "detailSiloCode": "DK2-1",
  "creatorName": "中控",
  "detailStatus": 1
}
```

### 混合条件查询
```json
{
  "page": 1,
  "pageSize": 10,
  "materialCode": "11000544",
  "status": 1,
  "detailSiloCode": "DK2-1",
  "creatorName": "中控",
  "actualStartTimeStart": "2025-06-01 00:00:00",
  "actualStartTimeEnd": "2025-06-30 23:59:59"
}
```

### 唯一标识查询
```json
{
  "page": 1,
  "pageSize": 10,
  "uniqueId": "DK2-1_20250627001"
}
```

## API接口

### 请求地址
```
POST /ricehusk/inbound-task/list/page/get
```

### 请求参数
参考`RiceHuskInboundTaskQueryDTO`类的字段定义

### 响应格式
```json
{
  "content": [
    {
      "id": "1",
      "orderNo": "RC250627001",
      "orderDate": "2025-06-27",
      "materialName": "DK",
      "materialCode": "11000544",
      "quantity": 52142.000000,
      "unit": "kg",
      "status": 1,
      "detailList": [
        {
          "id": 1,
          "taskId": 1,
          "siloName": "DK2-1",
          "siloCode": "DK2-1",
          "materialName": "DK",
          "materialCode": "11000544",
          "unit": "kg",
          "quantity": 52142.000000,
          "actualStartTime": "2025-06-16 23:44:26",
          "actualCompletionTime": "2025-06-16 23:44:26",
          "status": 1,
          "creatorName": "中控",
          "uniqueId": "DK2-1_20250627001"
        }
      ]
    }
  ],
  "totalElements": 1,
  "totalPages": 1,
  "size": 10,
  "number": 0
}
```

## 性能优化

1. **条件性JOIN**: 只有当查询条件包含子表字段时才执行LEFT JOIN
2. **索引优化**: 确保相关字段有适当的数据库索引
3. **分页查询**: 保持原有的分页功能，避免全表扫描

## 测试

提供了完整的单元测试和集成测试：

- `RiceHuskInboundTaskServiceTest`: 服务层测试
- `RiceHuskInboundTaskControllerTest`: 控制器层测试

测试覆盖了以下场景：
- 基本查询
- 主表条件查询
- 子表条件查询
- 混合条件查询
- 日期范围查询
- 唯一标识查询

## 注意事项

1. 当使用子表查询条件时，会自动进行LEFT JOIN操作
2. 使用DISTINCT确保主表记录不重复
3. 子表的详情数据通过collection标签延迟加载
4. 建议在相关字段上创建数据库索引以提高查询性能

## 兼容性

本次优化完全向后兼容，原有的查询功能不受影响，只是扩展了新的查询能力。
